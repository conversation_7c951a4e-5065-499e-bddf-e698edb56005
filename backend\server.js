const express = require('express');
const cors = require('cors');
const fs = require('fs');
const speech = require('@google-cloud/speech');
const app = express();
const port = 5000;

app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Path to your JSON key (using the existing googleCloud.json file)
const client = new speech.SpeechClient({
  keyFilename: '../googleCloud.json',
});

app.post('/speech-to-text', async (req, res) => {
  try {
    const audio = req.body.audioBase64;
    
    if (!audio) {
      return res.status(400).json({ error: 'No audio data provided' });
    }

    const audioBuffer = Buffer.from(audio, 'base64');
    const audioBytes = audioBuffer.toString('base64');

    const audioConfig = {
      content: audioBytes,
    };

    const config = {
      encoding: 'WEBM_OPUS', // Changed from LINEAR16 to support web audio
      sampleRateHertz: 48000, // Changed to match typical web audio
      languageCode: 'en-US',
      enableAutomaticPunctuation: true,
      model: 'latest_long', // Use latest model for better accuracy
    };

    const request = {
      audio: audioConfig,
      config: config,
    };

    const [response] = await client.recognize(request);
    const transcription = response.results
      .map(result => result.alternatives[0].transcript)
      .join('\n');
    
    console.log('Transcription:', transcription);
    res.json({ transcription });
  } catch (err) {
    console.error('Speech recognition error:', err);
    res.status(500).json({ 
      error: 'Speech recognition failed',
      details: err.message 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Speech-to-text server is running' });
});

app.listen(port, () => {
  console.log(`Speech-to-text server running on http://localhost:${port}`);
});
