{"name": "text-to-speech-backend", "version": "1.0.0", "description": "Backend server for text-to-speech application with Google Cloud Speech-to-Text", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "@google-cloud/speech": "^6.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["speech-to-text", "google-cloud", "express"], "author": "", "license": "ISC"}